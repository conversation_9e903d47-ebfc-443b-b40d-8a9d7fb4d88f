#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试脚本 - 验证错误修复
"""

import pandas as pd
from excel_processor import ExcelDataProcessor

def test_empty_data():
    """测试空数据处理"""
    print("测试空数据处理...")
    
    processor = ExcelDataProcessor()
    
    # 测试空数据
    empty_data = []
    summary = processor.summarize_data(empty_data)
    print(f"空数据汇总结果: {len(summary)} 行")
    
    # 测试包含空值的数据
    test_data = [
        pd.DataFrame({
            '搜索关键词': ['', None, 'test'],
            '单数': [0, 1, 2],
            '搜索主图': ['', '', ''],
            '商品ID': ['', '', ''],
            '需求备注': ['', '', '']
        })
    ]
    
    summary = processor.summarize_data(test_data)
    print(f"包含空值的数据汇总结果: {len(summary)} 行")
    
    return True

def test_split_logic():
    """测试拆分逻辑"""
    print("\n测试拆分逻辑...")
    
    processor = ExcelDataProcessor()
    
    # 创建测试数据
    test_data = [
        pd.DataFrame({
            '搜索关键词': ['关键词1', '关键词2'],
            '单数': [10, 5],
            '搜索主图': ['img1', 'img2'],
            '商品ID': ['id1', 'id2'],
            '需求备注': ['备注1', '备注2']
        })
    ]
    
    # 汇总
    summary = processor.summarize_data(test_data)
    print(f"汇总数据: {len(summary)} 行")
    
    # 拆分
    customer_services = ['张三', '李四']
    split_result = processor.split_data_by_customer_service(summary, customer_services)
    print(f"拆分结果: {len(split_result)} 行")
    
    # 显示分配结果
    for cs in customer_services:
        cs_data = split_result[split_result['客服'] == cs]
        total = cs_data['单数'].sum()
        print(f"  {cs}: {total}单")
    
    return True

def main():
    """主测试函数"""
    print("=" * 40)
    print("快速测试 - 验证错误修复")
    print("=" * 40)
    
    try:
        # 测试空数据处理
        test_empty_data()
        
        # 测试拆分逻辑
        test_split_logic()
        
        print("\n" + "=" * 40)
        print("✅ 所有测试通过！错误已修复。")
        print("=" * 40)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 