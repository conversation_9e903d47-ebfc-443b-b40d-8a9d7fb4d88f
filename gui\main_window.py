#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GUI主窗口模块
"""

import sys
import os
from PyQt6.QtWidgets import (QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, 
                             QPushButton, QLabel, QFileDialog, QTextEdit, 
                             QLineEdit, QTableWidget, QTableWidgetItem, QMessageBox,
                             QProgressBar, QGroupBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from .widgets import (
    FileSelectionWidget,
    CustomerServiceWidget,
    OutputConfigWidget,
    LogWidget
)
from .processor_thread import ExcelProcessorThread

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.excel_files = []
        self.customer_service_names = []
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("导入excel表格的显示")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 添加标题
        self._create_title(main_layout)
        
        # 添加文件选择组件
        self.file_widget = FileSelectionWidget()
        main_layout.addWidget(self.file_widget)
        
        # 添加客服配置组件
        self.cs_widget = CustomerServiceWidget()
        main_layout.addWidget(self.cs_widget)
        
        # 添加输出配置组件
        self.output_widget = OutputConfigWidget()
        main_layout.addWidget(self.output_widget)
        
        # 添加进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        main_layout.addWidget(self.progress_bar)
        
        # 添加日志组件
        self.log_widget = LogWidget()
        main_layout.addWidget(self.log_widget)
        
        # 添加操作按钮
        self._create_action_buttons(main_layout)
        
        # 设置整体样式
        self._apply_styles()
        
        # 初始化处理器
        self.processor = None
        
    def _create_title(self, layout):
        """创建标题"""
        title_label = QLabel("导入excel表格的显示")
        title_label.setFont(QFont("Microsoft YaHei", 18, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("功能：导入Excel表格，按Sheet为单位分配给不同客服，保持原Sheet结构和名称")
        desc_label.setFont(QFont("Microsoft YaHei", 10))
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #7f8c8d; margin-bottom: 10px;")
        layout.addWidget(desc_label)
    
    def _create_action_buttons(self, layout):
        """创建操作按钮"""
        btn_layout = QHBoxLayout()
        
        self.process_btn = QPushButton("开始处理")
        self.process_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.process_btn.clicked.connect(self.start_processing)
        btn_layout.addWidget(self.process_btn)
        
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.clear_log_btn.clicked.connect(self.log_widget.clear_log)
        btn_layout.addWidget(self.clear_log_btn)
        
        layout.addLayout(btn_layout)
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)
    
    def start_processing(self):
        """开始处理"""
        # 获取文件列表
        self.excel_files = self.file_widget.get_files()
        if not self.excel_files:
            QMessageBox.warning(self, "警告", "请先选择Excel文件！")
            return
        
        # 获取客服列表
        self.customer_service_names = self.cs_widget.get_customer_services()
        if not self.customer_service_names:
            QMessageBox.warning(self, "警告", "请先添加客服！")
            return
        
        # 获取输出目录
        output_dir = self.output_widget.get_output_dir()
        if not output_dir:
            QMessageBox.warning(self, "警告", "请先选择输出目录！")
            return
        
        # 禁用按钮
        self.process_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建并启动处理线程
        self.processor = ExcelProcessorThread(
            self.excel_files, 
            self.customer_service_names, 
            output_dir
        )
        self.processor.progress_updated.connect(self.progress_bar.setValue)
        self.processor.log_updated.connect(self.log_widget.add_log)
        self.processor.finished.connect(self.processing_finished)
        self.processor.start()
    
    def processing_finished(self, success, message):
        """处理完成"""
        self.process_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            QMessageBox.information(self, "成功", "处理完成！\n\n拆分后的文件已保存到输出目录。")
        else:
            QMessageBox.critical(self, "错误", f"处理失败: {message}") 