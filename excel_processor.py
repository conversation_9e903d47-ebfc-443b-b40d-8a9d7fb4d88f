import pandas as pd
import openpyxl
from openpyxl import load_workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.drawing.image import Image
import os
import copy
import math
from PIL import Image as PILImage
import io

class ExcelFormatProcessor:
    """Excel格式和图片处理类"""
    
    def __init__(self):
        self.original_workbooks = {}  # 存储原始工作簿的引用
        
    def load_original_workbook(self, file_path):
        """加载原始工作簿以保持格式"""
        if file_path not in self.original_workbooks:
            self.original_workbooks[file_path] = load_workbook(file_path)
        return self.original_workbooks[file_path]
    
    def copy_worksheet_format(self, source_ws, target_ws):
        """复制工作表格式"""
        try:
            # 复制列宽
            for col in source_ws.column_dimensions:
                target_ws.column_dimensions[col] = copy.copy(source_ws.column_dimensions[col])
            
            # 复制行高
            for row in source_ws.row_dimensions:
                target_ws.row_dimensions[row] = copy.copy(source_ws.row_dimensions[row])
            
            # 复制单元格格式
            for row in source_ws.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        target_cell = target_ws.cell(row=cell.row, column=cell.column)
                        target_cell.font = copy.copy(cell.font)
                        target_cell.fill = copy.copy(cell.fill)
                        target_cell.border = copy.copy(cell.border)
                        target_cell.alignment = copy.copy(cell.alignment)
                        target_cell.number_format = cell.number_format
                        
        except Exception as e:
            print(f"复制格式时出错: {str(e)}")
    
    def copy_images(self, source_ws, target_ws):
        """复制图片"""
        try:
            for image in source_ws._images:
                # 复制图片到目标工作表
                target_ws.add_image(image)
        except Exception as e:
            print(f"复制图片时出错: {str(e)}")
    
    def apply_default_formatting(self, worksheet, data_columns):
        """应用默认格式"""
        try:
            # 设置列宽
            column_widths = {
                'A': 15,  # 搜索主图
                'B': 15,  # 商品ID
                'C': 50,  # 搜索关键词
                'D': 10,  # 单数
                'E': 12,  # 合计单量
                'F': 15,  # 需求备注
                'G': 15,  # 客服
            }
            
            for col, width in column_widths.items():
                if col in worksheet.column_dimensions:
                    worksheet.column_dimensions[col].width = width
            
            # 设置标题行格式
            header_font = Font(bold=True, color="FFFFFF", size=12)
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            header_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 应用标题格式
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = header_border
            
            # 设置数据行格式
            data_alignment = Alignment(horizontal="left", vertical="center")
            data_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = data_alignment
                    cell.border = data_border
                    
        except Exception as e:
            print(f"应用默认格式时出错: {str(e)}")

class ExcelDataProcessor:
    """Excel数据处理类"""
    
    def __init__(self):
        self.format_processor = ExcelFormatProcessor()
        
    def read_excel_data(self, file_paths, progress_callback=None, log_callback=None):
        """读取多个Excel文件的数据"""
        all_data = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            if progress_callback:
                progress_callback(int((i / total_files) * 50))
            if log_callback:
                log_callback(f"正在读取文件: {os.path.basename(file_path)}")
            
            try:
                # 读取Excel文件的所有sheet
                excel_file = pd.ExcelFile(file_path)
                
                for sheet_name in excel_file.sheet_names:
                    try:
                        # 读取sheet数据
                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                        
                        # 检查是否包含必要的列
                        required_columns = ['搜索关键词', '单数']
                        if all(col in df.columns for col in required_columns):
                            # 添加文件信息
                            df['源文件'] = os.path.basename(file_path)
                            df['源Sheet'] = sheet_name
                            df['源文件路径'] = file_path
                            all_data.append(df)
                            if log_callback:
                                log_callback(f"  - 成功读取Sheet: {sheet_name}")
                        else:
                            if log_callback:
                                log_callback(f"  - 跳过Sheet {sheet_name}: 缺少必要列")
                                
                    except Exception as e:
                        if log_callback:
                            log_callback(f"  - 读取Sheet {sheet_name} 失败: {str(e)}")
                        
            except Exception as e:
                if log_callback:
                    log_callback(f"读取文件 {file_path} 失败: {str(e)}")
        
        return all_data
    
    def process_data_by_sheet(self, all_data, log_callback=None):
        """按Sheet处理数据，不汇总"""
        if log_callback:
            log_callback("正在按Sheet处理数据...")
        
        if not all_data:
            return []
        
        processed_sheets = []
        
        for df in all_data:
            # 检查是否有数据
            if df.empty:
                continue
            
            # 检查必要的列是否存在
            required_columns = ['搜索关键词', '单数']
            if not all(col in df.columns for col in required_columns):
                continue
            
            # 过滤掉空值
            df_clean = df.dropna(subset=['搜索关键词', '单数'])
            
            if df_clean.empty:
                continue
            
            # 按搜索关键词汇总当前Sheet的数据
            sheet_summary = []
            
            for keyword in df_clean['搜索关键词'].unique():
                if pd.isna(keyword) or keyword == '':
                    continue
                    
                keyword_data = df_clean[df_clean['搜索关键词'] == keyword]
                
                # 检查keyword_data是否为空
                if keyword_data.empty:
                    continue
                    
                total_quantity = keyword_data['单数'].sum()
                
                # 获取第一条记录的其他信息
                try:
                    first_row = keyword_data.iloc[0]
                    summary_row = {
                        '搜索主图': first_row.get('搜索主图', ''),
                        '商品ID': first_row.get('商品ID', ''),
                        '搜索关键词': keyword,
                        '单数': total_quantity,
                        '合计单量': total_quantity,
                        '需求备注': first_row.get('需求备注', ''),
                        '源文件': first_row.get('源文件', ''),
                        '源Sheet': first_row.get('源Sheet', ''),
                        '源文件路径': first_row.get('源文件路径', '')
                    }
                    sheet_summary.append(summary_row)
                except IndexError as e:
                    if log_callback:
                        log_callback(f"警告: 处理关键词 '{keyword}' 时出错: {str(e)}")
                    continue
            
            if sheet_summary:
                sheet_df = pd.DataFrame(sheet_summary)
                processed_sheets.append(sheet_df)
                if log_callback:
                    log_callback(f"处理Sheet: {sheet_df.iloc[0]['源Sheet']}，共 {len(sheet_summary)} 个关键词")
        
        if log_callback:
            log_callback(f"Sheet处理完成，共处理 {len(processed_sheets)} 个Sheet")
        
        return processed_sheets
    
    def split_data_by_customer_service(self, processed_sheets, customer_service_names, log_callback=None):
        """按Sheet为单位分配给客服"""
        if log_callback:
            log_callback("正在按Sheet为单位分配数据...")

        # 检查输入数据
        if not processed_sheets:
            if log_callback:
                log_callback("错误: 处理后的Sheet数据为空，无法拆分")
            return []

        if not customer_service_names:
            if log_callback:
                log_callback("错误: 客服列表为空，无法拆分")
            return []

        # 过滤空的Sheet
        valid_sheets = [sheet_df for sheet_df in processed_sheets if not sheet_df.empty]

        if not valid_sheets:
            if log_callback:
                log_callback("错误: 没有有效的Sheet数据，无法拆分")
            return []

        cs_count = len(customer_service_names)

        # 计算每个Sheet的总单数，用于分配决策
        sheet_info = []
        total_quantity = 0
        for i, sheet_df in enumerate(valid_sheets):
            sheet_quantity = sheet_df['单数'].sum()
            sheet_name = sheet_df.iloc[0]['源Sheet'] if not sheet_df.empty else f"Sheet_{i+1}"
            sheet_info.append({
                'index': i,
                'name': sheet_name,
                'quantity': sheet_quantity,
                'data': sheet_df
            })
            total_quantity += sheet_quantity
            if log_callback:
                log_callback(f"Sheet '{sheet_name}': {sheet_quantity} 单")

        if total_quantity == 0:
            if log_callback:
                log_callback("警告: 总单数为0，无需拆分")
            return []

        # 按单数从大到小排序Sheet，便于分配
        sheet_info.sort(key=lambda x: x['quantity'], reverse=True)

        # 初始化每个客服的分配情况
        cs_assignments = []
        for i, cs_name in enumerate(customer_service_names):
            cs_assignments.append({
                'name': cs_name,
                'total_quantity': 0,
                'sheets': []
            })

        if log_callback:
            log_callback(f"总单数: {total_quantity}, 客服数量: {cs_count}")
            log_callback("开始按Sheet分配...")

        # 使用贪心算法分配Sheet：每次将Sheet分配给当前总单数最少的客服
        for sheet in sheet_info:
            # 找到当前总单数最少的客服
            min_cs = min(cs_assignments, key=lambda x: x['total_quantity'])

            # 将Sheet分配给该客服
            min_cs['sheets'].append(sheet)
            min_cs['total_quantity'] += sheet['quantity']

            if log_callback:
                log_callback(f"Sheet '{sheet['name']}' ({sheet['quantity']}单) -> {min_cs['name']} (总计: {min_cs['total_quantity']}单)")

        # 生成最终的分配数据
        all_split_data = []
        for cs_assignment in cs_assignments:
            cs_name = cs_assignment['name']
            for sheet in cs_assignment['sheets']:
                sheet_df = sheet['data'].copy()
                # 为每行数据添加客服信息
                sheet_df['客服'] = cs_name
                all_split_data.append(sheet_df)

        if log_callback:
            log_callback("分配结果:")
            for cs_assignment in cs_assignments:
                sheet_names = [sheet['name'] for sheet in cs_assignment['sheets']]
                log_callback(f"  {cs_assignment['name']}: {cs_assignment['total_quantity']}单, Sheets: {', '.join(sheet_names)}")

        # 合并所有分配数据
        if all_split_data:
            combined_result = pd.concat(all_split_data, ignore_index=True)
            return [combined_result]
        else:
            return []
    
    def save_split_results(self, all_split_results, customer_service_names, output_dir, log_callback=None):
        """保存拆分结果（按Sheet为单位分配）"""
        if log_callback:
            log_callback("正在保存结果...")
        
        # 检查输入数据
        if not all_split_results:
            if log_callback:
                log_callback("错误: 拆分数据为空，无法保存")
            return
        
        if not customer_service_names:
            if log_callback:
                log_callback("错误: 客服列表为空，无法保存")
            return
        
        # 检查输出目录
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                if log_callback:
                    log_callback(f"创建输出目录: {output_dir}")
            except Exception as e:
                if log_callback:
                    log_callback(f"错误: 无法创建输出目录: {str(e)}")
                return
        
        # 获取拆分后的数据（现在只有一个DataFrame）
        split_df = all_split_results[0] if all_split_results else pd.DataFrame()
        
        if split_df.empty:
            if log_callback:
                log_callback("错误: 拆分数据为空，无法保存")
            return
        
        # 为每个客服创建一个Excel文件
        for cs_name in customer_service_names:
            try:
                # 获取当前客服的数据
                cs_data = split_df[split_df['客服'] == cs_name]
                
                if not cs_data.empty:
                    # 创建输出文件名
                    output_file = os.path.join(output_dir, f"{cs_name}_分配数据.xlsx")
                    
                    # 保存为Excel
                    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                        # 移除不需要的列
                        save_data = cs_data.drop(['源文件', '源Sheet', '源文件路径', '客服'], axis=1, errors='ignore')
                        
                        # 保存到工作表
                        save_data.to_excel(writer, sheet_name='分配数据', index=False)
                        
                        # 应用格式
                        worksheet = writer.sheets['分配数据']
                        self.format_processor.apply_default_formatting(worksheet, save_data.columns)
                        
                        # 尝试复制原文件的格式和图片
                        self.copy_original_format_and_images(cs_data, worksheet, writer)
                    
                    if log_callback:
                        log_callback(f"已保存 {cs_name} 的分配数据: {output_file}")
                else:
                    if log_callback:
                        log_callback(f"警告: {cs_name} 没有分配数据，跳过保存")
                        
            except Exception as e:
                if log_callback:
                    log_callback(f"错误: 保存 {cs_name} 的数据时出错: {str(e)}")
        
        if log_callback:
            log_callback("保存完成")
    
    def copy_original_format_and_images(self, cs_data, target_worksheet, writer):
        """复制原文件的格式和图片"""
        try:
            # 获取第一条记录的源文件信息
            if not cs_data.empty:
                first_row = cs_data.iloc[0]
                source_file_path = first_row.get('源文件路径', '')
                source_sheet_name = first_row.get('源Sheet', '')
                
                if source_file_path and os.path.exists(source_file_path):
                    # 加载原始工作簿
                    original_wb = self.format_processor.load_original_workbook(source_file_path)
                    
                    # 检查sheet_names属性是否存在
                    if hasattr(original_wb, 'sheet_names') and source_sheet_name in original_wb.sheet_names:
                        original_ws = original_wb[source_sheet_name]
                        
                        # 复制格式
                        self.format_processor.copy_worksheet_format(original_ws, target_worksheet)
                        
                        # 复制图片
                        self.format_processor.copy_images(original_ws, target_worksheet)
                        
        except Exception as e:
            # 静默处理格式复制错误，不影响主要功能
            pass
    
    def process_excel_files(self, excel_files, customer_service_names, output_dir, 
                          progress_callback=None, log_callback=None):
        """处理Excel文件的主流程（按Sheet分别处理）"""
        try:
            # 检查输入参数
            if not excel_files:
                return False, "未选择Excel文件"
            
            if not customer_service_names:
                return False, "未添加客服"
            
            if not output_dir:
                return False, "未选择输出目录"
            
            if log_callback:
                log_callback(f"开始处理 {len(excel_files)} 个文件，分配给 {len(customer_service_names)} 个客服")
            
            # 1. 读取数据
            all_data = self.read_excel_data(excel_files, progress_callback, log_callback)
            
            if not all_data:
                return False, "没有找到有效的数据"
            
            if log_callback:
                log_callback(f"成功读取 {len(all_data)} 个数据源")
            
            # 2. 按Sheet处理数据（不汇总）
            processed_sheets = self.process_data_by_sheet(all_data, log_callback)
            
            if not processed_sheets:
                return False, "Sheet处理失败，请检查Excel文件格式"
            
            if log_callback:
                log_callback(f"Sheet处理完成，共 {len(processed_sheets)} 个Sheet")
            
            # 3. 按Sheet为单位分配数据
            all_split_results = self.split_data_by_customer_service(processed_sheets, customer_service_names, log_callback)
            
            if not all_split_results:
                return False, "拆分数据失败"
            
            if log_callback:
                log_callback(f"拆分完成，共处理 {len(all_split_results)} 个Sheet")
            
            # 4. 保存结果
            self.save_split_results(all_split_results, customer_service_names, output_dir, log_callback)
            
            if log_callback:
                log_callback("所有处理步骤完成")
            
            return True, "处理成功"
            
        except Exception as e:
            error_msg = f"处理过程中出现错误: {str(e)}"
            if log_callback:
                log_callback(error_msg)
            return False, error_msg 