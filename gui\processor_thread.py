#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
处理线程模块
"""

from PyQt6.QtCore import QThread, pyqtSignal
from typing import List

from core import (
    ProcessingConfig,
    DataReader,
    DataProcessor,
    DataSplitter,
    ExcelFormatProcessor,
    ExcelWriter
)

class ExcelProcessorThread(QThread):
    """Excel处理线程"""
    
    progress_updated = pyqtSignal(int)
    log_updated = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, excel_files: List[str], customer_service_names: List[str], output_dir: str):
        super().__init__()
        self.excel_files = excel_files
        self.customer_service_names = customer_service_names
        self.output_dir = output_dir
        
        # 创建配置
        self.config = ProcessingConfig(
            progress_callback=self.progress_updated.emit,
            log_callback=self.log_updated.emit
        )
        
        # 创建处理器
        self.data_reader = DataReader(self.config)
        self.data_processor = DataProcessor(self.config)
        self.data_splitter = DataSplitter(self.config)
        
        # 创建Excel处理器
        self.excel_formatter = ExcelFormatProcessor()
        self.excel_writer = ExcelWriter(self.excel_formatter)
        
    def run(self):
        """运行处理流程"""
        try:
            # 检查输入参数
            if not self.excel_files:
                self.finished.emit(False, "未选择Excel文件")
                return
            
            if not self.customer_service_names:
                self.finished.emit(False, "未添加客服")
                return
            
            if not self.output_dir:
                self.finished.emit(False, "未选择输出目录")
                return
            
            self.log_updated.emit(f"开始处理 {len(self.excel_files)} 个文件，分配给 {len(self.customer_service_names)} 个客服")
            
            # 1. 读取数据
            all_data = self.data_reader.read_excel_files(self.excel_files)
            
            if not all_data:
                self.finished.emit(False, "没有找到有效的数据")
                return
            
            self.log_updated.emit(f"成功读取 {len(all_data)} 个数据源")
            
            # 2. 按Sheet处理数据
            processed_sheets = self.data_processor.process_sheets(all_data)
            
            if not processed_sheets:
                self.finished.emit(False, "Sheet处理失败，请检查Excel文件格式")
                return
            
            self.log_updated.emit(f"Sheet处理完成，共 {len(processed_sheets)} 个Sheet")
            
            # 3. 按总数平均拆分数据
            all_split_results = self.data_splitter.split_by_customer_service(
                processed_sheets, self.customer_service_names
            )
            
            if not all_split_results:
                self.finished.emit(False, "拆分数据失败")
                return
            
            self.log_updated.emit(f"拆分完成，共处理 {len(all_split_results)} 个结果")
            
            # 4. 保存结果
            self.excel_writer.save_split_results(
                all_split_results, self.customer_service_names, self.output_dir,
                self.log_updated.emit
            )
            
            self.log_updated.emit("所有处理步骤完成")
            self.finished.emit(True, "处理成功")
            
        except Exception as e:
            error_msg = f"处理过程中出现错误: {str(e)}"
            self.log_updated.emit(error_msg)
            self.finished.emit(False, error_msg) 