#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据处理核心模块
负责Excel数据的读取、处理和拆分
"""

import pandas as pd
import os
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

@dataclass
class ProcessingConfig:
    """处理配置"""
    required_columns: List[str] = None
    progress_callback: callable = None
    log_callback: callable = None
    
    def __post_init__(self):
        if self.required_columns is None:
            self.required_columns = ['搜索关键词', '单数', '行号']

class DataReader:
    """数据读取器"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
    
    def read_excel_files(self, file_paths: List[str]) -> List[pd.DataFrame]:
        """读取多个Excel文件的数据"""
        all_data = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            if self.config.progress_callback:
                self.config.progress_callback(int((i / total_files) * 50))
            if self.config.log_callback:
                self.config.log_callback(f"正在读取文件: {os.path.basename(file_path)}")
            
            try:
                # 使用openpyxl直接读取指定区域
                from openpyxl import load_workbook
                workbook = load_workbook(file_path)

                for sheet_name in workbook.sheetnames:
                    try:
                        worksheet = workbook[sheet_name]

                        # 读取C1-C10和D1-D10区域的数据
                        data_rows = []
                        for row in range(1, 11):  # 1到10行
                            c_value = worksheet[f'C{row}'].value  # 搜索关键词
                            d_value = worksheet[f'D{row}'].value  # 单数

                            # 只处理有效数据行
                            if c_value is not None and d_value is not None:
                                try:
                                    # 确保单数是数字
                                    d_value = float(d_value) if d_value != '' else 0
                                    if d_value > 0:  # 只处理大于0的单数
                                        data_rows.append({
                                            '行号': row,
                                            '搜索关键词': str(c_value).strip(),
                                            '单数': d_value,
                                            '源文件': os.path.basename(file_path),
                                            '源Sheet': sheet_name,
                                            '源文件路径': file_path
                                        })
                                except (ValueError, TypeError):
                                    # 跳过无法转换为数字的单数
                                    continue

                        if data_rows:
                            df = pd.DataFrame(data_rows)
                            all_data.append(df)
                            if self.config.log_callback:
                                self.config.log_callback(f"  - 成功读取Sheet: {sheet_name} ({len(data_rows)}条有效数据)")
                        else:
                            if self.config.log_callback:
                                self.config.log_callback(f"  - 跳过Sheet {sheet_name}: C1-C10和D1-D10区域无有效数据")

                    except Exception as e:
                        if self.config.log_callback:
                            self.config.log_callback(f"  - 读取Sheet {sheet_name} 失败: {str(e)}")
                        
            except Exception as e:
                if self.config.log_callback:
                    self.config.log_callback(f"读取文件 {file_path} 失败: {str(e)}")
        
        return all_data

class DataProcessor:
    """数据处理器"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
    
    def process_sheets(self, all_data: List[pd.DataFrame]) -> List[pd.DataFrame]:
        """按Sheet处理数据"""
        if self.config.log_callback:
            self.config.log_callback("正在按Sheet处理数据...")
        
        if not all_data:
            return []
        
        processed_sheets = []
        
        for df in all_data:
            # 检查是否有数据
            if df.empty:
                continue
            
            # 检查必要的列是否存在
            if not all(col in df.columns for col in self.config.required_columns):
                continue
            
            # 过滤掉空值
            df_clean = df.dropna(subset=self.config.required_columns)
            
            if df_clean.empty:
                continue
            
            # 按搜索关键词汇总当前Sheet的数据
            sheet_summary = self._summarize_sheet_data(df_clean)
            
            if sheet_summary:
                sheet_df = pd.DataFrame(sheet_summary)
                processed_sheets.append(sheet_df)
                if self.config.log_callback:
                    self.config.log_callback(f"处理Sheet: {sheet_df.iloc[0]['源Sheet']}，共 {len(sheet_summary)} 个关键词")
        
        if self.config.log_callback:
            self.config.log_callback(f"Sheet处理完成，共处理 {len(processed_sheets)} 个Sheet")
        
        return processed_sheets
    
    def _summarize_sheet_data(self, df: pd.DataFrame) -> List[Dict]:
        """汇总单个Sheet的数据"""
        sheet_summary = []
        
        for keyword in df['搜索关键词'].unique():
            if pd.isna(keyword) or keyword == '':
                continue
                
            keyword_data = df[df['搜索关键词'] == keyword]
            
            # 检查keyword_data是否为空
            if keyword_data.empty:
                continue
                
            total_quantity = keyword_data['单数'].sum()
            
            # 获取第一条记录的其他信息
            try:
                first_row = keyword_data.iloc[0]
                summary_row = {
                    '搜索主图': first_row.get('搜索主图', ''),
                    '商品ID': first_row.get('商品ID', ''),
                    '搜索关键词': keyword,
                    '单数': total_quantity,
                    '合计单量': total_quantity,
                    '需求备注': first_row.get('需求备注', ''),
                    '源文件': first_row.get('源文件', ''),
                    '源Sheet': first_row.get('源Sheet', ''),
                    '源文件路径': first_row.get('源文件路径', '')
                }
                sheet_summary.append(summary_row)
            except IndexError as e:
                if self.config.log_callback:
                    self.config.log_callback(f"警告: 处理关键词 '{keyword}' 时出错: {str(e)}")
                continue
        
        return sheet_summary

class DataSplitter:
    """数据拆分器"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
    
    def split_by_customer_service(self, processed_sheets: List[pd.DataFrame],
                                 customer_service_names: List[str]) -> List[pd.DataFrame]:
        """按Sheet为单位分配给客服"""
        if self.config.log_callback:
            self.config.log_callback("正在按Sheet为单位分配数据...")

        # 检查输入数据
        if not processed_sheets:
            if self.config.log_callback:
                self.config.log_callback("错误: 处理后的Sheet数据为空，无法拆分")
            return []

        if not customer_service_names:
            if self.config.log_callback:
                self.config.log_callback("错误: 客服列表为空，无法拆分")
            return []

        # 过滤空的Sheet
        valid_sheets = [sheet_df for sheet_df in processed_sheets if not sheet_df.empty]

        if not valid_sheets:
            if self.config.log_callback:
                self.config.log_callback("错误: 没有有效的Sheet数据，无法拆分")
            return []

        cs_count = len(customer_service_names)

        # 计算每个Sheet的总单数，用于分配决策
        sheet_info = []
        total_quantity = 0
        for i, sheet_df in enumerate(valid_sheets):
            sheet_quantity = sheet_df['单数'].sum()
            sheet_name = sheet_df.iloc[0]['源Sheet'] if not sheet_df.empty else f"Sheet_{i+1}"
            sheet_info.append({
                'index': i,
                'name': sheet_name,
                'quantity': sheet_quantity,
                'data': sheet_df
            })
            total_quantity += sheet_quantity
            if self.config.log_callback:
                self.config.log_callback(f"Sheet '{sheet_name}': {sheet_quantity} 单")

        if total_quantity == 0:
            if self.config.log_callback:
                self.config.log_callback("警告: 总单数为0，无需拆分")
            return []

        # 按单数从大到小排序Sheet，便于分配
        sheet_info.sort(key=lambda x: x['quantity'], reverse=True)

        # 初始化每个客服的分配情况
        cs_assignments = []
        for i, cs_name in enumerate(customer_service_names):
            cs_assignments.append({
                'name': cs_name,
                'total_quantity': 0,
                'sheets': []
            })

        if self.config.log_callback:
            self.config.log_callback(f"总单数: {total_quantity}, 客服数量: {cs_count}")
            self.config.log_callback("开始按Sheet分配...")

        # 使用贪心算法分配Sheet：每次将Sheet分配给当前总单数最少的客服
        for sheet in sheet_info:
            # 找到当前总单数最少的客服
            min_cs = min(cs_assignments, key=lambda x: x['total_quantity'])

            # 将Sheet分配给该客服
            min_cs['sheets'].append(sheet)
            min_cs['total_quantity'] += sheet['quantity']

            if self.config.log_callback:
                self.config.log_callback(f"Sheet '{sheet['name']}' ({sheet['quantity']}单) -> {min_cs['name']} (总计: {min_cs['total_quantity']}单)")

        # 生成最终的分配数据
        all_split_data = []
        for cs_assignment in cs_assignments:
            cs_name = cs_assignment['name']
            for sheet in cs_assignment['sheets']:
                sheet_df = sheet['data'].copy()
                # 为每行数据添加客服信息
                sheet_df['客服'] = cs_name
                all_split_data.append(sheet_df)

        if self.config.log_callback:
            self.config.log_callback("分配结果:")
            for cs_assignment in cs_assignments:
                sheet_names = [sheet['name'] for sheet in cs_assignment['sheets']]
                self.config.log_callback(f"  {cs_assignment['name']}: {cs_assignment['total_quantity']}单, Sheets: {', '.join(sheet_names)}")

        # 合并所有分配数据
        if all_split_data:
            combined_result = pd.concat(all_split_data, ignore_index=True)
            return [combined_result]
        else:
            return []