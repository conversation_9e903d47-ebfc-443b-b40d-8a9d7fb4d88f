#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Excel格式处理模块
负责Excel文件的格式设置和样式应用
"""

import openpyxl
from openpyxl import load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.drawing.image import Image
import copy
import os
from typing import Dict, List

class ExcelFormatProcessor:
    """Excel格式处理器"""
    
    def __init__(self):
        self.original_workbooks = {}  # 存储原始工作簿的引用
        
    def load_original_workbook(self, file_path: str) -> openpyxl.Workbook:
        """加载原始工作簿以保持格式"""
        if file_path not in self.original_workbooks:
            self.original_workbooks[file_path] = load_workbook(file_path)
        return self.original_workbooks[file_path]
    
    def copy_worksheet_format(self, source_ws: openpyxl.worksheet.worksheet.Worksheet, 
                             target_ws: openpyxl.worksheet.worksheet.Worksheet) -> None:
        """复制工作表格式"""
        try:
            # 复制列宽
            for col in source_ws.column_dimensions:
                target_ws.column_dimensions[col] = copy.copy(source_ws.column_dimensions[col])
            
            # 复制行高
            for row in source_ws.row_dimensions:
                target_ws.row_dimensions[row] = copy.copy(source_ws.row_dimensions[row])
            
            # 复制单元格格式
            for row in source_ws.iter_rows():
                for cell in row:
                    if cell.value is not None:
                        target_cell = target_ws.cell(row=cell.row, column=cell.column)
                        target_cell.font = copy.copy(cell.font)
                        target_cell.fill = copy.copy(cell.fill)
                        target_cell.border = copy.copy(cell.border)
                        target_cell.alignment = copy.copy(cell.alignment)
                        target_cell.number_format = cell.number_format
                        
        except Exception as e:
            print(f"复制格式时出错: {str(e)}")
    
    def copy_images(self, source_ws: openpyxl.worksheet.worksheet.Worksheet, 
                   target_ws: openpyxl.worksheet.worksheet.Worksheet) -> None:
        """复制图片"""
        try:
            for image in source_ws._images:
                # 复制图片到目标工作表
                target_ws.add_image(image)
        except Exception as e:
            print(f"复制图片时出错: {str(e)}")
    
    def apply_default_formatting(self, worksheet: openpyxl.worksheet.worksheet.Worksheet, 
                               data_columns: List[str]) -> None:
        """应用默认格式"""
        try:
            # 设置列宽
            column_widths = {
                'A': 15,  # 搜索主图
                'B': 15,  # 商品ID
                'C': 50,  # 搜索关键词
                'D': 10,  # 单数
                'E': 12,  # 合计单量
                'F': 15,  # 需求备注
                'G': 15,  # 客服
            }
            
            for col, width in column_widths.items():
                if col in worksheet.column_dimensions:
                    worksheet.column_dimensions[col].width = width
            
            # 设置标题行格式
            header_font = Font(bold=True, color="FFFFFF", size=12)
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            header_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            # 应用标题格式
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = header_border
            
            # 设置数据行格式
            data_alignment = Alignment(horizontal="left", vertical="center")
            data_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = data_alignment
                    cell.border = data_border
                    
        except Exception as e:
            print(f"应用默认格式时出错: {str(e)}")

class ExcelWriter:
    """Excel写入器"""
    
    def __init__(self, formatter: ExcelFormatProcessor):
        self.formatter = formatter
    
    def save_split_results(self, all_split_results: List, customer_service_names: List[str], 
                          output_dir: str, log_callback=None) -> None:
        """保存拆分结果"""
        if log_callback:
            log_callback("正在保存结果...")
        
        # 检查输入数据
        if not all_split_results:
            if log_callback:
                log_callback("错误: 拆分数据为空，无法保存")
            return
        
        if not customer_service_names:
            if log_callback:
                log_callback("错误: 客服列表为空，无法保存")
            return
        
        # 检查输出目录
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir)
                if log_callback:
                    log_callback(f"创建输出目录: {output_dir}")
            except Exception as e:
                if log_callback:
                    log_callback(f"错误: 无法创建输出目录: {str(e)}")
                return
        
        # 获取拆分后的数据（现在只有一个DataFrame）
        split_df = all_split_results[0] if all_split_results else None
        
        if split_df is None or split_df.empty:
            if log_callback:
                log_callback("错误: 拆分数据为空，无法保存")
            return
        
        # 为每个客服创建一个Excel文件
        for cs_name in customer_service_names:
            try:
                # 获取当前客服的数据
                cs_data = split_df[split_df['客服'] == cs_name]
                
                if not cs_data.empty:
                    # 创建输出文件名
                    output_file = os.path.join(output_dir, f"{cs_name}_分配数据.xlsx")
                    
                    # 保存为Excel
                    workbook = openpyxl.Workbook()
                    try:
                        worksheet = workbook.active
                        worksheet.title = '分配数据'

                        # 写入数据
                        self._write_data_to_worksheet(worksheet, cs_data)

                        # 应用格式
                        self.formatter.apply_default_formatting(worksheet, cs_data.columns)

                        # 尝试复制原文件的格式和图片
                        self._copy_original_format_and_images(cs_data, worksheet)

                        # 保存文件
                        workbook.save(output_file)
                    finally:
                        workbook.close()
                    
                    if log_callback:
                        log_callback(f"已保存 {cs_name} 的分配数据: {output_file}")
                else:
                    if log_callback:
                        log_callback(f"警告: {cs_name} 没有分配数据，跳过保存")
                        
            except Exception as e:
                if log_callback:
                    log_callback(f"错误: 保存 {cs_name} 的数据时出错: {str(e)}")
        
        if log_callback:
            log_callback("保存完成")
    
    def _write_data_to_worksheet(self, worksheet: openpyxl.worksheet.worksheet.Worksheet, 
                                data: 'pd.DataFrame') -> None:
        """将数据写入工作表"""
        # 写入标题行
        headers = ['搜索主图', '商品ID', '搜索关键词', '单数', '合计单量', '需求备注']
        for col, header in enumerate(headers, 1):
            worksheet.cell(row=1, column=col, value=header)
        
        # 写入数据行
        for row_idx, (_, row_data) in enumerate(data.iterrows(), 2):
            worksheet.cell(row=row_idx, column=1, value=row_data.get('搜索主图', ''))
            worksheet.cell(row=row_idx, column=2, value=row_data.get('商品ID', ''))
            worksheet.cell(row=row_idx, column=3, value=row_data.get('搜索关键词', ''))
            worksheet.cell(row=row_idx, column=4, value=row_data.get('单数', ''))
            worksheet.cell(row=row_idx, column=5, value=row_data.get('合计单量', ''))
            worksheet.cell(row=row_idx, column=6, value=row_data.get('需求备注', ''))
    
    def _copy_original_format_and_images(self, cs_data: 'pd.DataFrame', 
                                       target_worksheet: openpyxl.worksheet.worksheet.Worksheet) -> None:
        """复制原文件的格式和图片"""
        try:
            # 获取第一条记录的源文件信息
            if not cs_data.empty:
                first_row = cs_data.iloc[0]
                source_file_path = first_row.get('源文件路径', '')
                source_sheet_name = first_row.get('源Sheet', '')
                
                if source_file_path and os.path.exists(source_file_path):
                    # 加载原始工作簿
                    original_wb = self.formatter.load_original_workbook(source_file_path)
                    
                    # 检查sheet_names属性是否存在
                    if hasattr(original_wb, 'sheet_names') and source_sheet_name in original_wb.sheet_names:
                        original_ws = original_wb[source_sheet_name]
                        
                        # 复制格式
                        self.formatter.copy_worksheet_format(original_ws, target_worksheet)
                        
                        # 复制图片
                        self.formatter.copy_images(original_ws, target_worksheet)
                        
        except Exception as e:
            # 静默处理格式复制错误，不影响主要功能
            pass 