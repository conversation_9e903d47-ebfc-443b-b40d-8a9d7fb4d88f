#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GUI组件模块
"""

from PyQt6.QtWidgets import (QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                             QFileDialog, QTextEdit, QLineEdit, QTableWidget, 
                             QTableWidgetItem, QGroupBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

class FileSelectionWidget(QGroupBox):
    """文件选择组件"""
    
    def __init__(self):
        super().__init__("Excel文件选择")
        self.excel_files = []
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        layout = QVBoxLayout(self)
        
        # 文件选择按钮
        btn_layout = QHBoxLayout()
        self.select_files_btn = QPushButton("选择Excel文件")
        self.select_files_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.select_files_btn.clicked.connect(self.select_files)
        btn_layout.addWidget(self.select_files_btn)
        
        self.clear_files_btn = QPushButton("清空文件列表")
        self.clear_files_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.clear_files_btn.clicked.connect(self.clear_files)
        btn_layout.addWidget(self.clear_files_btn)
        
        layout.addLayout(btn_layout)
        
        # 文件列表显示
        self.files_text = QTextEdit()
        self.files_text.setMaximumHeight(100)
        self.files_text.setPlaceholderText("选择的Excel文件将显示在这里...")
        self.files_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f8f9fa;
            }
        """)
        layout.addWidget(self.files_text)
    
    def select_files(self):
        """选择Excel文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择Excel文件", "", "Excel Files (*.xlsx *.xls)"
        )
        if files:
            self.excel_files.extend(files)
            self.update_files_display()
    
    def clear_files(self):
        """清空文件列表"""
        self.excel_files.clear()
        self.update_files_display()
    
    def update_files_display(self):
        """更新文件显示"""
        if self.excel_files:
            self.files_text.setPlainText("\n".join(self.excel_files))
        else:
            self.files_text.clear()
    
    def get_files(self):
        """获取文件列表"""
        return self.excel_files

class CustomerServiceWidget(QGroupBox):
    """客服配置组件"""
    
    def __init__(self):
        super().__init__("客服配置")
        self.customer_service_names = []
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        layout = QVBoxLayout(self)
        
        # 客服名称输入
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("客服名称:"))
        self.cs_name_input = QLineEdit()
        self.cs_name_input.setPlaceholderText("输入客服名称，按回车添加")
        self.cs_name_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        self.cs_name_input.returnPressed.connect(self.add_customer_service)
        input_layout.addWidget(self.cs_name_input)
        
        self.add_cs_btn = QPushButton("添加")
        self.add_cs_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.add_cs_btn.clicked.connect(self.add_customer_service)
        input_layout.addWidget(self.add_cs_btn)
        
        layout.addLayout(input_layout)
        
        # 客服列表
        self.cs_table = QTableWidget()
        self.cs_table.setColumnCount(2)
        self.cs_table.setHorizontalHeaderLabels(["序号", "客服名称"])
        self.cs_table.setMaximumHeight(150)
        self.cs_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
                gridline-color: #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.cs_table)
        
        # 客服操作按钮
        btn_layout = QHBoxLayout()
        self.remove_cs_btn = QPushButton("删除选中")
        self.remove_cs_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        self.remove_cs_btn.clicked.connect(self.remove_customer_service)
        btn_layout.addWidget(self.remove_cs_btn)
        
        self.clear_cs_btn = QPushButton("清空列表")
        self.clear_cs_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.clear_cs_btn.clicked.connect(self.clear_customer_services)
        btn_layout.addWidget(self.clear_cs_btn)
        
        layout.addLayout(btn_layout)
    
    def add_customer_service(self):
        """添加客服"""
        name = self.cs_name_input.text().strip()
        if name and name not in self.customer_service_names:
            self.customer_service_names.append(name)
            self.update_cs_table()
            self.cs_name_input.clear()
    
    def remove_customer_service(self):
        """删除选中的客服"""
        current_row = self.cs_table.currentRow()
        if current_row >= 0:
            del self.customer_service_names[current_row]
            self.update_cs_table()
    
    def clear_customer_services(self):
        """清空客服列表"""
        self.customer_service_names.clear()
        self.update_cs_table()
    
    def update_cs_table(self):
        """更新客服表格"""
        self.cs_table.setRowCount(len(self.customer_service_names))
        for i, name in enumerate(self.customer_service_names):
            self.cs_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            self.cs_table.setItem(i, 1, QTableWidgetItem(name))
    
    def get_customer_services(self):
        """获取客服列表"""
        return self.customer_service_names

class OutputConfigWidget(QGroupBox):
    """输出配置组件"""
    
    def __init__(self):
        super().__init__("输出配置")
        self.output_dir = ""
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        layout = QHBoxLayout(self)
        
        layout.addWidget(QLabel("输出目录:"))
        self.output_path_label = QLabel("未选择")
        self.output_path_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-style: italic;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: #f8f9fa;
            }
        """)
        layout.addWidget(self.output_path_label)
        
        self.select_output_btn = QPushButton("选择目录")
        self.select_output_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        self.select_output_btn.clicked.connect(self.select_output_directory)
        layout.addWidget(self.select_output_btn)
    
    def select_output_directory(self):
        """选择输出目录"""
        directory = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if directory:
            self.output_dir = directory
            self.output_path_label.setText(directory)
    
    def get_output_dir(self):
        """获取输出目录"""
        return self.output_dir

class LogWidget(QGroupBox):
    """日志组件"""
    
    def __init__(self):
        super().__init__("处理日志")
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        layout = QVBoxLayout(self)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: #f8f9fa;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.log_text)
    
    def add_log(self, message):
        """添加日志"""
        self.log_text.append(message)
        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(
            self.log_text.verticalScrollBar().maximum()
        )
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear() 