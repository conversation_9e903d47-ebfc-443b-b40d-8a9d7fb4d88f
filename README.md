# 运营表格自动拆分程序

一个基于PyQt6的GUI应用程序，用于自动拆分运营表格数据并分配给不同客服。

## 功能特点

- 📊 **多文件处理**: 支持读取多个Excel文件和多个Sheet
- 🔍 **按Sheet处理**: 每个Sheet独立处理，按搜索关键词汇总
- 👥 **总数平均分配**: 将所有Sheet的数据按总数平均分配给不同客服
- 🎨 **格式保持**: 保持原Excel文件的格式和图片
- 📱 **现代化界面**: 美观的PyQt6图形界面
- 📝 **实时日志**: 显示处理进度和详细信息

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 选择Excel文件
- 点击"选择Excel文件"按钮
- 选择包含运营数据的Excel文件（支持.xlsx和.xls格式）
- 可以同时选择多个文件

### 3. 配置客服
- 在"客服名称"输入框中输入客服姓名
- 按回车键或点击"添加"按钮添加客服
- 可以添加多个客服，程序会自动平均分配数据

### 4. 选择输出目录
- 点击"选择目录"按钮
- 选择保存拆分后文件的目录

### 5. 开始处理
- 点击"开始处理"按钮
- 程序会自动读取、汇总、拆分数据
- 处理完成后会在输出目录生成每个客服的分配文件

## Excel文件格式要求

程序需要Excel文件包含以下列：
- **搜索主图**: 商品主图信息
- **商品ID**: 商品唯一标识
- **搜索关键词**: 用于搜索的关键词
- **单数**: 订单数量
- **合计单量**: 总订单数量
- **需求备注**: 备注信息

## 输出文件

程序会为每个客服生成一个Excel文件，命名格式为：`{客服名称}_分配数据.xlsx`

每个输出文件包含：
- 分配给该客服的数据
- 保持原文件的格式和样式
- 包含原文件的图片（如果存在）

## 处理逻辑

1. **数据读取**: 读取所有Excel文件的所有Sheet
2. **按Sheet处理**: 每个Sheet独立处理，按搜索关键词汇总单数
3. **总数平均拆分**: 将所有Sheet的数据按总数平均分配给客服
4. **格式保持**: 复制原文件的格式和图片
5. **文件保存**: 为每个客服生成独立的Excel文件

## 示例

假设有以下数据：

**文件1.xlsx - Sheet1:**
- 搜索关键词A: 10单
- 搜索关键词B: 8单

**文件1.xlsx - Sheet2:**
- 搜索关键词C: 5单
- 搜索关键词A: 3单

**文件2.xlsx - Sheet3:**
- 搜索关键词B: 6单

有3个客服：张三、李四、王五

**处理过程：**

1. **按Sheet汇总**: 
   - Sheet1: 关键词A(10单) + 关键词B(8单) = 18单
   - Sheet2: 关键词C(5单) + 关键词A(3单) = 8单
   - Sheet3: 关键词B(6单) = 6单

2. **总数平均分配** (总32单):
   - 张三: 11单 (基础10单 + 余数1单)
   - 李四: 11单 (基础10单 + 余数1单)
   - 王五: 10单 (基础10单)

**最终结果：**
每个客服获得一个Excel文件，包含从各个Sheet分配的数据，总单数接近平均。

## 系统要求

- Python 3.7+
- Windows 10/11 (推荐)
- 支持其他操作系统（需要安装PyQt6）

## 故障排除

### 常见问题

1. **找不到必要列**: 确保Excel文件包含"搜索关键词"和"单数"列
2. **文件读取失败**: 检查Excel文件是否损坏或格式不正确
3. **权限错误**: 确保有输出目录的写入权限

### 日志信息

程序会显示详细的处理日志，包括：
- 文件读取状态
- 数据汇总信息
- 拆分分配结果
- 错误信息（如果有）

## 技术架构

- **GUI框架**: PyQt6
- **数据处理**: pandas
- **Excel处理**: openpyxl
- **图片处理**: PIL (Pillow)

## 许可证

MIT License 