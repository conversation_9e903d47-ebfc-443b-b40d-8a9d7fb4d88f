import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.drawing.image import Image
import os

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 创建示例数据
    sample_data = [
        {
            '搜索主图': 'product1.jpg',
            '商品ID': '959024154199',
            '搜索关键词': '狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠',
            '单数': 2,
            '合计单量': 2,
            '需求备注': '收藏加购'
        },
        {
            '搜索主图': 'product2.jpg',
            '商品ID': '959024154200',
            '搜索关键词': '情趣用品成人用品女用自慰器震动棒',
            '单数': 3,
            '合计单量': 3,
            '需求备注': '好评返现'
        },
        {
            '搜索主图': 'product3.jpg',
            '商品ID': '959024154201',
            '搜索关键词': '按摩器颈椎按摩仪肩颈按摩器',
            '单数': 1,
            '合计单量': 1,
            '需求备注': '无'
        },
        {
            '搜索主图': 'product4.jpg',
            '商品ID': '959024154202',
            '搜索关键词': '狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠',
            '单数': 5,
            '合计单量': 5,
            '需求备注': '收藏加购'
        },
        {
            '搜索主图': 'product5.jpg',
            '商品ID': '959024154203',
            '搜索关键词': '情趣用品成人用品女用自慰器震动棒',
            '单数': 2,
            '合计单量': 2,
            '需求备注': '好评返现'
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(sample_data)
    
    # 保存为Excel文件
    output_file = '示例运营表格.xlsx'
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='运营数据', index=False)
        
        # 获取工作表
        worksheet = writer.sheets['运营数据']
        
        # 设置列宽
        column_widths = {
            'A': 15,  # 搜索主图
            'B': 15,  # 商品ID
            'C': 50,  # 搜索关键词
            'D': 10,  # 单数
            'E': 12,  # 合计单量
            'F': 15,  # 需求备注
        }
        
        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width
        
        # 设置标题行格式
        header_font = Font(bold=True, color="FFFFFF", size=12)
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        header_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 应用标题格式
        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = header_border
        
        # 设置数据行格式
        data_alignment = Alignment(horizontal="left", vertical="center")
        data_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in worksheet.iter_rows(min_row=2):
            for cell in row:
                cell.alignment = data_alignment
                cell.border = data_border
    
    print(f"示例Excel文件已创建: {output_file}")
    print("文件包含以下数据:")
    print("- 搜索关键词: 狐狸肛塞尾巴女用玩具情趣肛门自慰器男同菊花前列腺按摩后庭拉珠 (总计7单)")
    print("- 搜索关键词: 情趣用品成人用品女用自慰器震动棒 (总计5单)")
    print("- 搜索关键词: 按摩器颈椎按摩仪肩颈按摩器 (总计1单)")
    print("\n可以用于测试程序的拆分功能。")

if __name__ == "__main__":
    create_sample_excel() 